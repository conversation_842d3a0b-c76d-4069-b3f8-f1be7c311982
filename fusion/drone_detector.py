import torch
import torch.nn as nn
from feature_extractor import FeatureExtractor, FeaturePyramidNetwork

class DroneDetectionModel(nn.Module):
    def __init__(self, num_classes=1):
        super(DroneDetectionModel, self).__init__()
        
        # 特征提取器
        self.feature_extractor = FeatureExtractor()
        
        # 特征金字塔网络
        self.fpn = FeaturePyramidNetwork()
        
        # 检测头
        self.detection_heads = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(256, 256, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 4 + num_classes, kernel_size=1)  # 4 for bbox, num_classes for classification
            ) for _ in range(3)  # 对应FPN的三个尺度
        ])
        
    def forward(self, image, radar_points):
        """
        前向传播
        Args:
            image: 输入图像 [B, C, H, W]
            radar_points: 雷达点云数据 [B, N, 3]
        Returns:
            predictions: 多尺度预测结果
        """
        # 提取特征
        features = self.feature_extractor(image, radar_points)
        
        # 构建多尺度特征
        multi_scale_features = []
        x = features
        for _ in range(3):
            x = nn.functional.max_pool2d(x, kernel_size=2, stride=2)
            multi_scale_features.append(x)
        
        # FPN处理
        fpn_features = self.fpn(multi_scale_features)
        
        # 多尺度预测
        predictions = []
        for features, head in zip(fpn_features, self.detection_heads):
            pred = head(features)
            predictions.append(pred)
        
        return predictions

def compute_loss(predictions, targets):
    """
    计算多尺度检测损失
    Args:
        predictions: 多尺度预测结果
        targets: 目标信息，包含boxes和labels
    Returns:
        total_loss: 总损失
    """
    # 分类损失
    cls_loss = nn.CrossEntropyLoss()
    
    # 边界框回归损失
    box_loss = nn.SmoothL1Loss()
    
    total_loss = 0
    for pred in predictions:
        # 分离分类和边界框预测
        cls_pred = pred[..., 4:]
        box_pred = pred[..., :4]
        
        # 计算分类损失
        cls_loss_value = cls_loss(cls_pred, targets['labels'])
        
        # 计算边界框损失
        box_loss_value = box_loss(box_pred, targets['boxes'])
        
        # 总损失
        total_loss += cls_loss_value + box_loss_value
    
    return total_loss

def post_process(predictions, conf_threshold=0.5, nms_threshold=0.4):
    """
    后处理：非极大值抑制
    Args:
        predictions: 多尺度预测结果
        conf_threshold: 置信度阈值
        nms_threshold: NMS阈值
    Returns:
        final_boxes: 最终检测框
        final_scores: 最终置信度
        final_labels: 最终类别
    """
    all_boxes = []
    all_scores = []
    all_labels = []
    
    for pred in predictions:
        # 分离预测结果
        boxes = pred[..., :4]
        scores = pred[..., 4:]
        
        # 获取最高置信度的类别
        max_scores, labels = torch.max(scores, dim=-1)
        
        # 应用置信度阈值
        mask = max_scores > conf_threshold
        boxes = boxes[mask]
        scores = max_scores[mask]
        labels = labels[mask]
        
        all_boxes.append(boxes)
        all_scores.append(scores)
        all_labels.append(labels)
    
    # 合并所有尺度的预测结果
    all_boxes = torch.cat(all_boxes, dim=0)
    all_scores = torch.cat(all_scores, dim=0)
    all_labels = torch.cat(all_labels, dim=0)
    
    # 执行NMS
    keep = nms(all_boxes, all_scores, nms_threshold)
    
    return all_boxes[keep], all_scores[keep], all_labels[keep]

def nms(boxes, scores, threshold):
    """
    非极大值抑制
    """
    keep = []
    indices = torch.argsort(scores, descending=True)
    
    while indices.numel() > 0:
        i = indices[0]
        keep.append(i)
        
        if indices.numel() == 1:
            break
            
        # 计算IoU
        ious = box_iou(boxes[i].unsqueeze(0), boxes[indices[1:]])
        
        # 保留IoU小于阈值的框
        mask = ious < threshold
        indices = indices[1:][mask]
    
    return torch.tensor(keep, dtype=torch.long)

def box_iou(boxes1, boxes2):
    """
    计算IoU
    """
    area1 = box_area(boxes1)
    area2 = box_area(boxes2)
    
    lt = torch.max(boxes1[:, None, :2], boxes2[:, :2])
    rb = torch.min(boxes1[:, None, 2:], boxes2[:, 2:])
    
    wh = (rb - lt).clamp(min=0)
    inter = wh[:, :, 0] * wh[:, :, 1]
    
    union = area1[:, None] + area2 - inter
    
    return inter / union

def box_area(boxes):
    """
    计算框的面积
    """
    return (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1]) 