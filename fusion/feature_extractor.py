import torch
import torch.nn as nn
import torch.nn.functional as F

class FeatureExtractor(nn.Module):
    def __init__(self, in_channels=3, radar_channels=3):
        """
        特征提取网络
        Args:
            in_channels: 输入图像通道数
            radar_channels: 雷达数据通道数
        """
        super(FeatureExtractor, self).__init__()
        
        # 图像特征提取分支
        self.image_branch = nn.Sequential(
            # 第一层卷积块
            nn.Conv2d(in_channels, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第二层卷积块
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第三层卷积块
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
        )
        
        # 雷达特征提取分支
        self.radar_branch = nn.Sequential(
            nn.Linear(radar_channels, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Conv2d(512, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, image, radar_points):
        """
        前向传播
        Args:
            image: 输入图像 [B, C, H, W]
            radar_points: 雷达点云数据 [B, N, 3]
        Returns:
            fused_features: 融合后的特征
        """
        # 提取图像特征
        image_features = self.image_branch(image)  # [B, 256, H/8, W/8]
        
        # 提取雷达特征
        B, N, C = radar_points.shape
        radar_features = self.radar_branch(radar_points.view(-1, C))  # [B*N, 256]
        radar_features = radar_features.view(B, N, 256)
        
        # 将雷达特征投影到图像特征图大小
        H, W = image_features.shape[2:]
        radar_features = radar_features.mean(dim=1)  # [B, 256]
        radar_features = radar_features.view(B, 256, 1, 1)
        radar_features = radar_features.expand(-1, -1, H, W)
        
        # 特征融合
        concat_features = torch.cat([image_features, radar_features], dim=1)
        fused_features = self.fusion_layer(concat_features)
        
        return fused_features

class FeaturePyramidNetwork(nn.Module):
    """
    特征金字塔网络，用于多尺度特征提取
    """
    def __init__(self, in_channels=256, out_channels=256):
        super(FeaturePyramidNetwork, self).__init__()
        
        # 横向连接
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_channels, out_channels, 1)
            for _ in range(3)
        ])
        
        # 自顶向下路径
        self.fpn_convs = nn.ModuleList([
            nn.Conv2d(out_channels, out_channels, 3, padding=1)
            for _ in range(3)
        ])
        
    def forward(self, features):
        """
        Args:
            features: 输入特征列表 [C2, C3, C4, C5]
        Returns:
            fpn_features: 多尺度特征列表 [P2, P3, P4, P5]
        """
        laterals = [
            lateral_conv(feature)
            for feature, lateral_conv in zip(features, self.lateral_convs)
        ]
        
        # 自顶向下路径
        for i in range(len(laterals)-1, 0, -1):
            laterals[i-1] += F.interpolate(
                laterals[i],
                size=laterals[i-1].shape[-2:],
                mode='nearest'
            )
        
        # 最终输出
        fpn_features = [
            fpn_conv(lateral)
            for lateral, fpn_conv in zip(laterals, self.fpn_convs)
        ]
        
        return fpn_features 