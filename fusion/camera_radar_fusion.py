import numpy as np
import cv2
from scipy.spatial import KDTree

class CameraRadarFusion:
    def __init__(self, image_shape):
        """
        初始化相机-雷达融合类
        Args:
            image_shape: 图像尺寸 (height, width)
        """
        self.image_shape = image_shape
        self.feature_radius = 5  # 局部特征提取的半径
        
    def project_radar_to_image(self, radar_points):
        """
        将雷达点云投影到图像平面
        Args:
            radar_points: n*3的雷达点云数据
        Returns:
            projected_points: 投影后的2D点坐标
        """
        # 由于数据已经标定，这里直接使用x,y坐标
        projected_points = radar_points[:, :2]
        return projected_points
    
    def extract_local_features(self, image, radar_points):
        """
        提取雷达点周围的局部特征
        Args:
            image: RGB图像
            radar_points: 投影后的雷达点坐标
        Returns:
            local_features: 局部特征向量
        """
        local_features = []
        for point in radar_points:
            x, y = int(point[0]), int(point[1])
            if 0 <= x < self.image_shape[1] and 0 <= y < self.image_shape[0]:
                # 提取局部区域
                x1 = max(0, x - self.feature_radius)
                y1 = max(0, y - self.feature_radius)
                x2 = min(self.image_shape[1], x + self.feature_radius)
                y2 = min(self.image_shape[0], y + self.feature_radius)
                
                local_patch = image[y1:y2, x1:x2]
                if local_patch.size > 0:
                    # 计算局部区域的特征（这里使用简单的颜色统计特征）
                    mean_color = np.mean(local_patch, axis=(0, 1))
                    std_color = np.std(local_patch, axis=(0, 1))
                    feature = np.concatenate([mean_color, std_color])
                    local_features.append(feature)
                else:
                    local_features.append(np.zeros(6))  # 3个通道的均值和标准差
            else:
                local_features.append(np.zeros(6))
        
        return np.array(local_features)
    
    def fuse_features(self, image, radar_points):
        """
        融合相机和雷达特征
        Args:
            image: RGB图像
            radar_points: n*3的雷达点云数据
        Returns:
            fused_features: 融合后的特征
        """
        # 1. 投影雷达点到图像平面
        projected_points = self.project_radar_to_image(radar_points)
        
        # 2. 提取局部特征
        local_features = self.extract_local_features(image, projected_points)
        
        # 3. 结合雷达深度信息
        radar_depth = radar_points[:, 2]  # 使用雷达点的z坐标作为深度信息
        radar_depth = radar_depth.reshape(-1, 1)
        
        # 4. 特征融合
        fused_features = np.concatenate([local_features, radar_depth], axis=1)
        
        return fused_features
    
    def visualize_fusion(self, image, radar_points, fused_features):
        """
        可视化融合结果
        Args:
            image: RGB图像
            radar_points: n*3的雷达点云数据
            fused_features: 融合后的特征
        Returns:
            vis_image: 可视化结果
        """
        vis_image = image.copy()
        projected_points = self.project_radar_to_image(radar_points)
        
        # 根据深度信息设置颜色
        depths = radar_points[:, 2]
        min_depth, max_depth = np.min(depths), np.max(depths)
        
        for i, point in enumerate(projected_points):
            x, y = int(point[0]), int(point[1])
            if 0 <= x < self.image_shape[1] and 0 <= y < self.image_shape[0]:
                # 根据深度值设置颜色（从蓝色到红色）
                depth = depths[i]
                color_ratio = (depth - min_depth) / (max_depth - min_depth)
                color = (
                    int(255 * color_ratio),  # B
                    0,                       # G
                    int(255 * (1 - color_ratio))  # R
                )
                cv2.circle(vis_image, (x, y), 3, color, -1)
        
        return vis_image 