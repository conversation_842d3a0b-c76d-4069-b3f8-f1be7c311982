# 使用官方ROS镜像作为基础
FROM ros:noetic

# 安装必要的ROS包和Python依赖
RUN apt-get update && apt-get install -y \
    python3-pip \
    python3-rosbag \
    python3-sensor-msgs \
    python3-cv-bridge \
    python3-numpy \
    python3-opencv \
    && rm -rf /var/lib/apt/lists/*

# 安装其他Python依赖
RUN pip3 install pillow

# 设置工作目录
WORKDIR /app

# 复制您的脚本和数据
COPY dataset/ /app/dataset/

# 创建输出目录
RUN mkdir -p /data/home/<USER>/anti-UAV/AV-FDTI/Data/np_data_align \
    /data/home/<USER>/anti-UAV/AV-FDTI/Data/image

# 设置环境变量
ENV PYTHONPATH=/opt/ros/noetic/lib/python3/dist-packages:$PYTHONPATH

# 默认命令
CMD ["python3", "/app/dataset/dataset_build.py"]