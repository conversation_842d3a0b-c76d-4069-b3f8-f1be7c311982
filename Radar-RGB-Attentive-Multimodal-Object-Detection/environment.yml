name: nuscenes
channels:
  - anaconda
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _tflow_select=2.1.0=gpu
  - absl-py=0.7.1=py37_0
  - astor=0.8.0=py37_0
  - attrs=19.1.0=py_0
  - backcall=0.1.0=py_0
  - blas=1.0=mkl
  - bleach=3.1.0=py_0
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.15.0=h7b6447c_1001
  - ca-certificates=2019.6.16=hecc5488_0
  - cairo=1.14.12=h8948797_3
  - certifi=2019.6.16=py37_1
  - cloudpickle=1.2.1=py_0
  - cudatoolkit=9.0=h13b8566_0
  - cudnn=7.6.0=cuda9.0_0
  - cupti=9.0.176=0
  - cytoolz=0.10.0=py37h7b6447c_0
  - dask-core=2.2.0=py_0
  - dbus=1.13.6=h746ee38_0
  - decorator=4.4.0=py37_1
  - entrypoints=0.3=py37_1000
  - expat=2.2.6=he6710b0_0
  - ffmpeg=4.0=hcdf2ecd_0
  - fontconfig=2.13.0=h9420a91_0
  - freeglut=3.0.0=hf484d3e_5
  - freetype=2.9.1=h8a8886c_1
  - gast=0.2.2=py37_0
  - geos=3.7.1=he6710b0_0
  - glib=2.56.2=hd408876_0
  - google-pasta=0.1.7=py_0
  - graphite2=1.3.13=h23475e2_0
  - grpcio=1.16.1=py37hf8bcb03_1
  - gst-plugins-base=1.14.0=hbbd80ab_1
  - gstreamer=1.14.0=hb453b48_1
  - h5py=2.8.0=py37h989c5e5_3
  - harfbuzz=1.8.8=hffaf4a1_0
  - hdf5=1.10.2=hba1933b_1
  - icu=58.2=h9c2bf20_1
  - imageio=2.5.0=py37_0
  - imgaug=0.2.9=py_1
  - intel-openmp=2019.4=243
  - ipython_genutils=0.2.0=py_1
  - jasper=2.0.14=h07fcdf6_1
  - jinja2=2.10.1=py_0
  - jpeg=9b=h024ee3a_2
  - jupyter_client=5.3.1=py_0
  - jupyter_console=6.0.0=py_0
  - jupyter_core=4.4.0=py_0
  - keras=2.2.4=0
  - keras-applications=1.0.8=py_0
  - keras-base=2.2.4=py37_0
  - keras-preprocessing=1.1.0=py_1
  - kiwisolver=1.1.0=py37he6710b0_0
  - libedit=3.1.20181209=hc058e9b_0
  - libffi=3.2.1=hd88cf55_4
  - libgcc-ng=9.1.0=hdf63c60_0
  - libgfortran-ng=7.3.0=hdf63c60_0
  - libglu=9.0.0=hf484d3e_1
  - libopencv=3.4.2=hb342d67_1
  - libopus=1.3=h7b6447c_0
  - libpng=1.6.37=hbc83047_0
  - libprotobuf=3.8.0=hd408876_0
  - libsodium=1.0.17=h516909a_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - libtiff=4.0.10=h2733197_2
  - libuuid=1.0.3=h1bed415_2
  - libvpx=1.7.0=h439df22_0
  - libxcb=1.13=h1bed415_1
  - libxml2=2.9.9=hea5a465_1
  - markdown=3.1.1=py37_0
  - markupsafe=1.1.1=py37h14c3975_0
  - mistune=0.8.4=py37h14c3975_1000
  - mkl=2019.4=243
  - mkl-service=2.0.2=py37h7b6447c_0
  - mkl_fft=1.0.12=py37ha843d7b_0
  - mkl_random=1.0.2=py37hd81dba3_0
  - ncurses=6.1=he6710b0_1
  - networkx=2.3=py_0
  - numpy-base=1.16.1=py37hde5b4d6_1
  - olefile=0.46=py37_0
  - opencv=3.4.2=py37h6fd60c2_1
  - openssl=1.1.1c=h516909a_0
  - pandoc=2.7.3=0
  - parso=0.5.1=py_0
  - pcre=8.43=he6710b0_0
  - pexpect=4.7.0=py37_0
  - pillow=6.1.0=py37h34e0f95_0
  - pip=19.1.1=py37_0
  - pixman=0.38.0=h7b6447c_0
  - prometheus_client=0.7.1=py_0
  - prompt_toolkit=2.0.9=py_0
  - protobuf=3.8.0=py37he6710b0_0
  - ptyprocess=0.6.0=py_1001
  - py-opencv=3.4.2=py37hb342d67_1
  - pygments=2.4.2=py_0
  - pyparsing=2.4.0=py_0
  - pyqt=5.9.2=py37h05f1152_2
  - python=3.7.3=h0371630_0
  - python-dateutil=2.8.0=py37_0
  - pytz=2019.1=py_0
  - pywavelets=1.0.3=py37hdd07704_1
  - pyyaml=5.1.1=py37h7b6447c_0
  - qt=5.9.7=h5867ecd_1
  - readline=7.0=h7b6447c_5
  - scikit-image=0.15.0=py37he6710b0_0
  - send2trash=1.5.0=py_0
  - setuptools=41.0.1=py37_0
  - shapely=1.6.4=py37h86c5351_0
  - sip=4.19.8=py37hf484d3e_0
  - sqlite=3.29.0=h7b6447c_0
  - tensorboard=1.14.0=py37hf484d3e_0
  - tensorflow=1.14.0=gpu_py37hae64822_0
  - tensorflow-base=1.14.0=gpu_py37h8f37b9b_0
  - tensorflow-estimator=1.14.0=py_0
  - tensorflow-gpu=1.14.0=h0d30ee6_0
  - termcolor=1.1.0=py37_1
  - testpath=0.4.2=py_1001
  - tk=8.6.8=hbc83047_0
  - toolz=0.10.0=py_0
  - tornado=6.0.3=py37h7b6447c_0
  - tqdm=4.32.2=py_0
  - werkzeug=0.15.4=py_0
  - wheel=0.33.4=py37_0
  - wrapt=1.11.2=py37h7b6447c_0
  - xz=5.2.4=h14c3975_4
  - yaml=0.1.7=had09818_2
  - zeromq=4.3.2=he1b5a44_2
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.3.7=h0b5b093_0
  - pip:
    - cachetools==3.1.1
    - clr==1.0.3
    - cycler==0.10.0
    - cython==0.29.13
    - defusedxml==0.6.0
    - descartes==1.1.0
    - detectron==0.0.0
    - ipykernel==5.1.1
    - ipython==7.6.1
    - ipython-genutils==0.2.0
    - ipywidgets==7.5.0
    - jedi==0.14.1
    - joblib==0.13.2
    - jsonschema==3.0.1
    - jupyter==1.0.0
    - jupyter-console==6.0.0
    - jupyter-core==4.5.0
    - matplotlib==3.1.1
    - nbconvert==5.5.0
    - nbformat==4.4.0
    - notebook==5.7.8
    - numpy==1.16.4
    - opencv-python==********
    - pandocfilters==1.4.2
    - pickleshare==0.7.5
    - pycocotools==2.0
    - pyquaternion==0.9.5
    - pyrsistent==0.15.3
    - pyzmq==18.0.2
    - qtconsole==4.5.1
    - scikit-learn==0.21.2
    - scipy==1.3.0
    - six==1.12.0
    - terminado==0.8.2
    - traitlets==4.3.2
    - wcwidth==0.1.7
    - webencodings==0.5.1
    - widgetsnbextension==3.5.0
prefix: /home/<USER>/anaconda3/envs/nuscenes

