import os
import torch

base_dir = '/data/home/<USER>/anti-UAV/dataset'
data_dir = os.path.join(base_dir, 'data')

CONFIG = {
    "dark_aug": 100, # Simulate pure dark environment
    "audio_seq": 1,
    "workers": 8,
    "kernel_num": 32,
    "feature_dim": 512,
    "checkpoint_path": '/data/home/<USER>/anti-UAV/dataset/output/best_epoch.pth',
    "base_dir": base_dir,
    "annotation_lines_train": os.path.join(data_dir, 'anotation_split/train.txt'),
    "annotation_lines_val": os.path.join(data_dir, 'anotation_split/val.txt'),
    "audio_path": os.path.join(data_dir, 'np_data_align'),
    "image_path": os.path.join(data_dir, 'image'),
    "detect_path": os.path.join(data_dir, 'Detection'),
    "gt_path": os.path.join(data_dir, 'label'),
    "num_class": 6,
    "device": torch.device('cuda:0' if torch.cuda.is_available() else 'cpu'),
    "confusion_matrix_path": 'confusion_matrix_d.png'
}
