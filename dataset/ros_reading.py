import rosbag
import os
import numpy as np
import cv2

print("当前工作目录：", os.getcwd())


Mavic_bag = rosbag.Bag('dataset/Mavic3.bag')
for topic, msg, t in Mavic_bag.read_messages():
    print("topic:{}, Type:{},Timestamp:{}".format(topic, type(msg), t))
    """if topic == '/usb_cam/image_raw':
        # 将ROS图像消息转换为OpenCV格式
        try:
            cv_image = bridge.imgmsg_to_cv2(msg, desired_encoding="bgr8")
            print("图像数据的shape为：", cv_image.shape)
        except Exception as e:
            print("图像数据转换失败:", e)"""
    if topic == "/audio1/audio" or topic == "/audio2/audio" or topic == "/audio3/audio" or topic == "/audio4/audio":
        # 假设音频数据为一维数组，可以直接转换为numpy数组
        audio_np = np.frombuffer(msg.data, dtype=np.int16)
        print("音频数据的shape为：", audio_np.shape)


