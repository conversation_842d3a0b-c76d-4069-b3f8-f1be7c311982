#!/usr/bin/env python3
import rosbag
import os
import numpy as np
import argparse
from collections import defaultdict

"""看一下label里面包含了什么信息
topic: /leica/point/absolute
(这是来自Leica测量系统的绝对坐标点数据
坐标值非常大 (x≈1005, y≈2020, z≈98)
这些是全局坐标系中的位置，可能是基于某个固定参考点的世界坐标系）  

/leica/point/relative
这是来自Leica测量系统的相对坐标点数据
坐标值较小 (x≈5.5, y≈19.2, z≈-0.42)
这些是相对于某个参考点的坐标，可能是相对于起飞点或测量站的位置

/rosout
这是ROS的标准日志输出话题，包含系统日志信息
只有6条消息，可能是系统启动或错误信息

/tf 
这是ROS的坐标变换话题，用于发布不同坐标系之间的变换关系
消息数量是/leica/point话题的两倍，可能每个点都有对应的坐标变换
"""

def extract_groundtruth(bag_path):
    """提取bag文件中的groundtruth/label数据"""
    print(f"\n{'='*80}")
    print(f"分析文件: {os.path.basename(bag_path)}")
    print(f"{'='*80}")
    
    if not os.path.exists(bag_path):
        print(f"错误: 文件 {bag_path} 不存在")
        return
    
    try:
        bag = rosbag.Bag(bag_path)
    except Exception as e:
        print(f"打开bag文件时出错: {e}")
        return
    
    # 获取话题信息
    topic_info = defaultdict(lambda: {'count': 0, 'type': None})
    
    # 计算消息数量和类型
    for topic, msg, t in bag.read_messages():
        topic_info[topic]['count'] += 1
        if topic_info[topic]['type'] is None:
            topic_info[topic]['type'] = type(msg).__name__
    
    # 打印话题信息
    print("\n话题信息:")
    print(f"{'话题名称':<40} {'消息类型':<30} {'消息数量':<10}")
    print("-" * 80)
    for topic, info in sorted(topic_info.items()):
        print(f"{topic:<40} {info['type']:<30} {info['count']:<10}")
    
    # 找出可能包含groundtruth/label数据的话题
    potential_gt_topics = []
    for topic, info in topic_info.items():
        if ('ground' in topic.lower() or 
            'truth' in topic.lower() or 
            'pose' in topic.lower() or 
            'position' in topic.lower() or 
            'label' in topic.lower() or
            'Pose' in info['type'] or
            'Point' in info['type']):
            potential_gt_topics.append(topic)
    
    if not potential_gt_topics:
        print("\n未找到明显的groundtruth/label话题，将分析所有话题")
        potential_gt_topics = list(topic_info.keys())
    
    # 详细分析可能的groundtruth/label话题
    print("\n\n详细分析可能的groundtruth/label话题:")
    for topic in potential_gt_topics:
        print(f"\n话题: {topic} (类型: {topic_info[topic]['type']})")
        
        # 显示多个样本
        sample_count = 0
        for _, msg, t in bag.read_messages(topics=[topic]):
            if sample_count >= 200:  # 显示前10个样本
                break
            
            print(f"\n  样本 {sample_count+1} (时间戳: {t}):")
            
            # 尝试提取所有可能的坐标/位置信息
            try:
                # 检查是否有pose属性
                if hasattr(msg, 'pose'):
                    pose = msg.pose
                    print(f"    位置: x={pose.position.x}, y={pose.position.y}, z={pose.position.z}")
                    print(f"    方向: x={pose.orientation.x}, y={pose.orientation.y}, z={pose.orientation.z}, w={pose.orientation.w}")
                
                # 检查是否直接有position属性
                elif hasattr(msg, 'position'):
                    print(f"    位置: x={msg.position.x}, y={msg.position.y}, z={msg.position.z}")
                    if hasattr(msg, 'orientation'):
                        print(f"    方向: x={msg.orientation.x}, y={msg.orientation.y}, z={msg.orientation.z}, w={msg.orientation.w}")
                
                # 检查是否有point属性
                elif hasattr(msg, 'point'):
                    print(f"    点坐标: x={msg.point.x}, y={msg.point.y}, z={msg.point.z}")
                
                # 检查是否有x,y,z属性
                elif hasattr(msg, 'x') and hasattr(msg, 'y') and hasattr(msg, 'z'):
                    print(f"    坐标: x={msg.x}, y={msg.y}, z={msg.z}")
                
                # 检查是否有data属性，可能是数组形式的坐标
                elif hasattr(msg, 'data') and isinstance(msg.data, (list, tuple)) and len(msg.data) >= 3:
                    print(f"    数据: {msg.data[:10]}...")  # 只显示前10个元素
                
                # 尝试打印所有非方法属性
                else:
                    attrs = dir(msg)
                    data_attrs = [attr for attr in attrs if not attr.startswith('_') and attr not in ['serialize', 'deserialize']]
                    
                    for attr in data_attrs:
                        try:
                            value = getattr(msg, attr)
                            if not callable(value):
                                print(f"    {attr}: {value}")
                        except:
                            pass
            
            except Exception as e:
                print(f"    分析消息时出错: {e}")
            
            sample_count += 1
    
    bag.close()
    print("\n分析完成")

def main():
    parser = argparse.ArgumentParser(description='提取ROS bag文件中的groundtruth/label数据')
    parser.add_argument('--bag', default='/data/home/<USER>/anti-UAV/dataset/groundtruth_m300.bag',
                        help='要分析的bag文件路径')
    
    args = parser.parse_args()
    
    print(f"分析文件: {args.bag}")
    extract_groundtruth(args.bag)

if __name__ == '__main__':
    main()
