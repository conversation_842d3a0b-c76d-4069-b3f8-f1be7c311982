#!/usr/bin/env python3
import rosbag
import os
import numpy as np
from tqdm import tqdm
import argparse
from collections import defaultdict
import glob

def count_files_by_prefix(directory, prefix):
    """统计指定目录中以特定前缀开头的文件数量"""
    pattern = os.path.join(directory, f"{prefix}_*.npy")
    files = glob.glob(pattern)
    return len(files)

def count_files_by_prefix_image(directory, prefix):
    """统计指定目录中以特定前缀开头的图像文件数量"""
    pattern = os.path.join(directory, f"{prefix}_*.png")
    files = glob.glob(pattern)
    return len(files)

def extract_groundtruth(bag_path, output_dir, drone_type, image_dir, audio_dir):
    """
    从bag文件中提取groundtruth数据并保存为.npy格式
    
    Args:
        bag_path: bag文件路径
        output_dir: 输出目录
        drone_type: 无人机类型，用于文件名前缀
        image_dir: 图像数据目录
        audio_dir: 音频数据目录
    """
    print(f"\n{'='*80}")
    print(f"处理文件: {os.path.basename(bag_path)}")
    print(f"无人机类型: {drone_type}")
    print(f"{'='*80}")
    
    if not os.path.exists(bag_path):
        print(f"错误: 文件 {bag_path} 不存在")
        return
    
    try:
        bag = rosbag.Bag(bag_path)
    except Exception as e:
        print(f"打开bag文件时出错: {e}")
        return
    
    # 获取话题信息
    topic_info = defaultdict(lambda: {'count': 0, 'type': None})
    
    # 计算消息数量和类型
    for topic, msg, t in bag.read_messages():
        topic_info[topic]['count'] += 1
        if topic_info[topic]['type'] is None:
            topic_info[topic]['type'] = type(msg).__name__
    
    # 打印话题信息
    print("\n话题信息:")
    print(f"{'话题名称':<40} {'消息类型':<30} {'消息数量':<10}")
    print("-" * 80)
    for topic, info in sorted(topic_info.items()):
        print(f"{topic:<40} {info['type']:<30} {info['count']:<10}")
    
    # 指定使用/leica/point/absolute话题作为groundtruth数据源
    gt_topic = '/leica/point/absolute'
    
    # 检查话题是否存在
    if gt_topic not in topic_info:
        print(f"错误: 话题 {gt_topic} 在bag文件中不存在")
        # 尝试查找替代话题
        if '/leica/point/relative' in topic_info:
            gt_topic = '/leica/point/relative'
            print(f"使用替代话题: {gt_topic}")
        else:
            print("未找到可用的Leica点位话题，无法提取groundtruth数据")
            return
    
    print(f"\n使用话题 {gt_topic} 作为groundtruth数据源...")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取groundtruth数据
    gt_data = []
    timestamps = []
    
    print("提取groundtruth数据...")
    for _, msg, t in tqdm(bag.read_messages(topics=[gt_topic]), total=topic_info[gt_topic]['count']):
        try:
            # 根据消息类型提取坐标
            if hasattr(msg, 'x') and hasattr(msg, 'y') and hasattr(msg, 'z'):
                # Point类型消息
                x, y, z = msg.x, msg.y, msg.z
            elif hasattr(msg, 'point'):
                # PointStamped类型消息
                x, y, z = msg.point.x, msg.point.y, msg.point.z
            else:
                print(f"无法从消息中提取坐标: {type(msg).__name__}")
                continue
            
            # 保存坐标和时间戳
            gt_data.append([x, y, z])
            timestamps.append(t.to_sec())
            
        except Exception as e:
            print(f"处理消息时出错: {e}")
    
    if not gt_data:
        print("未提取到任何groundtruth数据")
        return
    
    # 将数据转换为numpy数组
    gt_data = np.array(gt_data, dtype=np.float32)
    timestamps = np.array(timestamps, dtype=np.float64)
    
    # 保存数据
    count = 0
    for i in range(len(gt_data)):
        # 使用无人机类型和计数器作为文件名
        filename = f"{drone_type}_{count:05d}.npy"
        output_path = os.path.join(output_dir, filename)
        
        # 保存单个坐标点
        np.save(output_path, gt_data[i])
        count += 1
    
    print(f"完成! 共保存了 {count} 个groundtruth数据点到 {output_dir}")
    
    # 统计现有的图像和音频数据
    existing_image_count = count_files_by_prefix_image(image_dir, drone_type)
    existing_audio_count = count_files_by_prefix(audio_dir, drone_type)
    
    print(f"\n{drone_type} 数据集统计:")
    print(f"{'数据类型':<15} {'数量':<10}")
    print("-" * 25)
    print(f"{'Label':<15} {count:<10}")
    print(f"{'Image':<15} {existing_image_count:<10}")
    print(f"{'Audio':<15} {existing_audio_count:<10}")
    
    # 检查数量是否一致
    if count == existing_image_count and count == existing_audio_count:
        print(f"\n✅ {drone_type} 数据集数量一致")
    else:
        print(f"\n⚠️ {drone_type} 数据集数量不一致")
        if count != existing_image_count:
            print(f"  - {drone_type} Label数量 ({count}) 与 Image数量 ({existing_image_count}) 不匹配")
        if count != existing_audio_count:
            print(f"  - {drone_type} Label数量 ({count}) 与 Audio数量 ({existing_audio_count}) 不匹配")
    
    bag.close()
    return count

def main():
    parser = argparse.ArgumentParser(description='从ROS bag文件中提取groundtruth数据并保存为.npy格式')
    parser.add_argument('--output_dir', default='/data/home/<USER>/anti-UAV/AV-FDTI/Data/label',
                        help='输出目录')
    parser.add_argument('--image_dir', default='/data/home/<USER>/anti-UAV/AV-FDTI/Data/image',
                        help='图像数据目录')
    parser.add_argument('--audio_dir', default='/data/home/<USER>/anti-UAV/AV-FDTI/Data/np_data_align',
                        help='音频数据目录')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 要处理的bag文件列表 - 确保顺序为m300, mavic2, mavic3, pham4
    bag_files = [
        ('/data/home/<USER>/anti-UAV/dataset/groundtruth_m300.bag', 'm300'),
        ('/data/home/<USER>/anti-UAV/dataset/groundtruth_mavic2.bag', 'mavic2'),
        ('/data/home/<USER>/anti-UAV/dataset/groundtruth_mavic3.bag', 'mavic3'),
        ('/data/home/<USER>/anti-UAV/dataset/groundtruth_pham4.bag', 'p4')
    ]
    
    # 总统计
    total_stats = {
        'label': 0,
        'image': 0,
        'audio': 0
    }
    
    # 每种无人机类型的统计
    drone_stats = {}
    
    # 处理每个bag文件
    for bag_path, drone_type in bag_files:
        if os.path.exists(bag_path):
            label_count = extract_groundtruth(bag_path, args.output_dir, drone_type, args.image_dir, args.audio_dir)
            if label_count:
                # 更新总统计
                total_stats['label'] += label_count
                image_count = count_files_by_prefix_image(args.image_dir, drone_type)
                audio_count = count_files_by_prefix(args.audio_dir, drone_type)
                total_stats['image'] += image_count
                total_stats['audio'] += audio_count
                
                # 更新每种无人机类型的统计
                drone_stats[drone_type] = {
                    'label': label_count,
                    'image': image_count,
                    'audio': audio_count
                }
        else:
            print(f"警告: 文件 {bag_path} 不存在，跳过")
    
    # 打印每种无人机类型的统计
    print("\n" + "="*50)
    print("各无人机类型数据统计:")
    print("="*50)
    
    for drone_type in ['m300', 'mavic2', 'mavic3', 'p4']:
        if drone_type in drone_stats:
            stats = drone_stats[drone_type]
            print(f"\n无人机类型: {drone_type}")
            print(f"{'数据类型':<15} {'数量':<10}")
            print("-" * 25)
            print(f"{'Label':<15} {stats['label']:<10}")
            print(f"{'Image':<15} {stats['image']:<10}")
            print(f"{'Audio':<15} {stats['audio']:<10}")
            
            # 检查数量是否一致
            if stats['label'] == stats['image'] and stats['label'] == stats['audio']:
                print(f"✅ {drone_type} 数据集数量一致")
            else:
                print(f"⚠️ {drone_type} 数据集数量不一致")
                if stats['label'] != stats['image']:
                    print(f"  - Label数量 ({stats['label']}) 与 Image数量 ({stats['image']}) 不匹配")
                if stats['label'] != stats['audio']:
                    print(f"  - Label数量 ({stats['label']}) 与 Audio数量 ({stats['audio']}) 不匹配")
    
    # 打印总统计
    print("\n" + "="*50)
    print("总数据集统计:")
    print(f"{'数据类型':<15} {'数量':<10}")
    print("-" * 25)
    print(f"{'Label':<15} {total_stats['label']:<10}")
    print(f"{'Image':<15} {total_stats['image']:<10}")
    print(f"{'Audio':<15} {total_stats['audio']:<10}")
    
    # 检查总数量是否一致
    if total_stats['label'] == total_stats['image'] and total_stats['label'] == total_stats['audio']:
        print("\n✅ 总数据集数量一致")
    else:
        print("\n⚠️ 总数据集数量不一致")
        if total_stats['label'] != total_stats['image']:
            print(f"  - 总Label数量 ({total_stats['label']}) 与总Image数量 ({total_stats['image']}) 不匹配")
        if total_stats['label'] != total_stats['audio']:
            print(f"  - 总Label数量 ({total_stats['label']}) 与总Audio数量 ({total_stats['audio']}) 不匹配")
    
    print("\n所有文件处理完成!")

if __name__ == '__main__':
    main()
