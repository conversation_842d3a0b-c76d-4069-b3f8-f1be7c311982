import rosbag
import os
import numpy as np
import cv2
import math
import struct
from io import BytesIO
from PIL import Image

# 设置输出目录 - 使用修改后的路径
output_audio_dir = "/data/home/<USER>/anti-UAV/AV-FDTI(modified)/Data/np_data_align"
output_image_dir = "/data/home/<USER>/anti-UAV/AV-FDTI(modified)/Data/image"
os.makedirs(output_audio_dir, exist_ok=True)
os.makedirs(output_image_dir, exist_ok=True)

# 要处理的bag文件列表 - 使用完整的绝对路径
bag_files = [
    "/data/home/<USER>/anti-UAV/dataset/M300.bag",
    "/data/home/<USER>/anti-UAV/dataset/Mavic2.bag",
    "/data/home/<USER>/anti-UAV/dataset/Mavic3.bag",
    "/data/home/<USER>/anti-UAV/dataset/Pham4.bag",
    "/data/home/<USER>/anti-UAV/dataset/Avata.bag"
]

# groundtruth bag 路径
GT_BAG_PATH = [
    "/data/home/<USER>/anti-UAV/dataset/groundtruth_m300.bag",
    "/data/home/<USER>/anti-UAV/dataset/groundtruth_mavic2.bag",
    "/data/home/<USER>/anti-UAV/dataset/groundtruth_mavic3.bag",
    "/data/home/<USER>/anti-UAV/dataset/groundtruth_pham4.bag",
    "/data/home/<USER>/anti-UAV/dataset/groundtruth_avata.bag"
]

# 用于查找最接近时间戳的消息
def find_closest_message(target_timestamp, messages):
    closest_msg = None
    min_time_diff = float('inf')
    for msg, t in messages:
        time_diff = abs(target_timestamp - t.to_sec())
        if time_diff < min_time_diff:
            min_time_diff = time_diff
            closest_msg = msg
    return closest_msg, min_time_diff

# 查找现有数据的最大编号
def find_max_existing_number(directory, prefix):
    """
    查找指定目录中以prefix开头的文件的最大编号
    例如：avata_00001.png -> 返回 1
    """
    max_num = -1
    if os.path.exists(directory):
        for filename in os.listdir(directory):
            if filename.startswith(prefix + "_") and filename.endswith((".png", ".npy")):
                try:
                    # 提取编号部分
                    num_str = filename.split("_")[1].split(".")[0]
                    num = int(num_str)
                    max_num = max(max_num, num)
                except (ValueError, IndexError):
                    continue
    return max_num

# 直接从ROS图像消息解析图像数据
def parse_image_msg(msg):
    try:
        # 检查是否是压缩图像
        if hasattr(msg, 'format') and 'compressed' in getattr(msg, 'format', ''):
            # 处理压缩图像
            np_arr = np.frombuffer(msg.data, np.uint8)
            return cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        
        # 处理原始图像
        if hasattr(msg, 'height') and hasattr(msg, 'width') and hasattr(msg, 'encoding'):
            height = msg.height
            width = msg.width
            encoding = msg.encoding
            
            if encoding == 'bgr8':
                return np.frombuffer(msg.data, dtype=np.uint8).reshape(height, width, 3)
            elif encoding == 'rgb8':
                img = np.frombuffer(msg.data, dtype=np.uint8).reshape(height, width, 3)
                return cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            elif encoding == 'mono8':
                img = np.frombuffer(msg.data, dtype=np.uint8).reshape(height, width)
                return cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        
        # 尝试作为JPEG或PNG处理
        try:
            img = Image.open(BytesIO(msg.data))
            return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
        except:
            pass
        
        # 如果以上方法都失败，打印消息属性以帮助调试
        print("图像消息属性:", dir(msg))
        print("无法解析图像消息，返回空图像")
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    except Exception as e:
        print(f"解析图像消息时出错: {e}")
        return np.zeros((480, 640, 3), dtype=np.uint8)

def load_groundtruth_messages(gt_bag_path, gt_topic='/leica/point/absolute'):
    gt_messages = []
    if not os.path.exists(gt_bag_path):
        print(f"警告: groundtruth bag {gt_bag_path} 不存在")
        return gt_messages
    try:
        gt_bag = rosbag.Bag(gt_bag_path)
        for topic, msg, t in gt_bag.read_messages():
            if topic == gt_topic:
                # 提取x, y, z
                if hasattr(msg, 'x') and hasattr(msg, 'y') and hasattr(msg, 'z'):
                    gt_messages.append((np.array([msg.x, msg.y, msg.z], dtype=np.float32), t.to_sec()))
                elif hasattr(msg, 'point'):
                    gt_messages.append((np.array([msg.point.x, msg.point.y, msg.point.z], dtype=np.float32), t.to_sec()))
        gt_bag.close()
        print(f"加载 groundtruth bag: {gt_bag_path}, 共 {len(gt_messages)} 条 groundtruth 数据")
    except Exception as e:
        print(f"读取 groundtruth bag 时出错: {e}")
    return gt_messages

def find_closest_index(target_timestamp, messages):
    min_time_diff = float('inf')
    min_idx = -1
    for i, (_, t) in enumerate(messages):
        time_diff = abs(target_timestamp - t)
        if time_diff < min_time_diff:
            min_time_diff = time_diff
            min_idx = i
    return min_idx, min_time_diff

# 处理每个bag文件和对应的groundtruth bag
for bag_path, gt_bag_path in zip(bag_files, GT_BAG_PATH):
    if not os.path.exists(bag_path) or not os.path.exists(gt_bag_path):
        print(f"警告: {bag_path} 或 {gt_bag_path} 不存在，跳过")
        continue
    bag_file = os.path.basename(bag_path)
    prefix = os.path.splitext(bag_file)[0].lower()
    print(f"\n处理 {bag_file} (GT: {os.path.basename(gt_bag_path)}) ...")
    # 查找现有数据的最大编号
    max_existing_audio = find_max_existing_number(output_audio_dir, prefix)
    max_existing_image = find_max_existing_number(output_image_dir, prefix)
    max_existing = max(max_existing_audio, max_existing_image)
    processed_count = max_existing + 1
    # 读取bag内音频和图像
    audio_messages = {k: [] for k in ["/audio1/audio", "/audio2/audio", "/audio3/audio", "/audio4/audio"]}
    image_messages = []
    try:
        bag = rosbag.Bag(bag_path)
        for topic, msg, t in bag.read_messages():
            if topic in audio_messages:
                audio_messages[topic].append((msg, t.to_sec()))
            if topic == '/usb_cam/image_raw':
                image_messages.append((msg, t.to_sec()))
        bag.close()
    except Exception as e:
        print(f"读取 {bag_file} 时出错: {e}")
        continue
    # 预处理音频组（以四路为一组，计算中间timestamp）
    min_audio_len = min(len(audio_messages[topic]) for topic in audio_messages)
    audio_groups = []
    for i in range(min_audio_len):
        audio_data_list = []
        timestamps = []
        for topic in audio_messages:
            msg, t = audio_messages[topic][i]
            audio_np = np.frombuffer(msg.data, dtype=np.int16)
            audio_data_list.append(audio_np)
            timestamps.append(t)
        combined_audio = np.concatenate(audio_data_list)
        center_timestamp = np.mean(timestamps)
        audio_groups.append((combined_audio, center_timestamp))
    # 读取groundtruth
    gt_messages = load_groundtruth_messages(gt_bag_path)
    print(f"GT数量: {len(gt_messages)}, 图像数量: {len(image_messages)}, 音频组数量: {len(audio_groups)}")
    # 遍历每个groundtruth，找最近的image和audio组
    for gt_idx, (gt_xyz, gt_time) in enumerate(gt_messages):
        # 找最近的image
        img_idx, img_time_diff = find_closest_index(gt_time, image_messages)
        # 找最近的audio组
        audio_idx, audio_time_diff = find_closest_index(gt_time, audio_groups)
        if img_idx == -1 or audio_idx == -1:
            print(f"GT {gt_idx} 没有找到合适的图像或音频，跳过")
            continue
        # 保存音频
        audio_filename = os.path.join(output_audio_dir, f"{prefix}_{processed_count:05d}.npy")
        np.save(audio_filename, audio_groups[audio_idx][0])
        # 保存图像
        try:
            cv_image = parse_image_msg(image_messages[img_idx][0])
            if cv_image is not None and cv_image.size > 0:
                image_filename = os.path.join(output_image_dir, f"{prefix}_{processed_count:05d}.png")
                cv2.imwrite(image_filename, cv_image)
            else:
                print(f"图像处理失败，跳过此数据对")
                continue
        except Exception as e:
            print(f"转换或保存图像时出错: {e}")
            continue
        # 保存groundtruth
        gt_filename = os.path.join(output_audio_dir, f"{prefix}_{processed_count:05d}_gt.npy")
        np.save(gt_filename, gt_xyz)
        print(f"保存: {audio_filename}, {image_filename}, {gt_filename} (GT时间: {gt_time:.4f}, imgΔt: {img_time_diff:.4f}, audioΔt: {audio_time_diff:.4f})")
        processed_count += 1
print("\n数据处理完成。所有groundtruth都已利用。")
