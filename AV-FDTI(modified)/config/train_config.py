import os

base_dir = '/data/home/<USER>/anti-UAV/AV-FDTI'
data_dir = os.path.join(base_dir, 'Data')

CONFIG = {
    "dark_aug": 1,
    "audio_seq": 1,
    "workers": 4,
    "batchsize": 32,
    "dropout_rate": 0.3,
    "kernel_num": 32,
    "num_class": 6,
    "feature_dim": 512,
    "epochs": 200,
    "learning_rate": 0.0001,
    "checkpoint_path": '',
    "base_dir": base_dir,
    "annotation_lines_train": os.path.join(data_dir, 'anotation_split_50/train.txt'),#训练集的标注文件
    "annotation_lines_val": os.path.join(data_dir, 'anotation_split_50/val.txt'),#验证集的标注文件
    "audio_path": os.path.join(data_dir, 'np_data_align'),#音频文件的路径
    "image_path": os.path.join(data_dir, 'image'),#图像文件的路径
    #"detect_path": os.path.join(data_dir, 'Detection_new'),#检测文件的路径
    "gt_path": os.path.join(data_dir, 'label'),#目标文件的路径
    "save_path": 'output/',#保存模型的路径
}
os.makedirs(CONFIG["save_path"], exist_ok=True)
