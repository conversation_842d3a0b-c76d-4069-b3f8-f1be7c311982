import torch
from tqdm import tqdm

def train_one_epoch(model, train_dataloader, optimizer, device, cross_entropy_loss):
    model.train()
    total_loss = 0
    with tqdm(total=len(train_dataloader), unit='batch') as pbar:
        for data in train_dataloader:
            # 只需要音频、图像和分类标签
            spec, image, _, _, _, cls, _ = (d.to(device) for d in data)
            optimizer.zero_grad()

            # 模型只输出分类结果
            c = model(spec, image)
            loss_cls = cross_entropy_loss(c, cls)

            # 只使用分类损失
            loss = loss_cls
            total_loss += loss.item()
            loss.backward()
            optimizer.step()
            pbar.update(1)

    return total_loss / len(train_dataloader)


def validate_one_epoch(model, val_dataloader, device, cross_entropy_loss):
    model.eval()
    total_loss = 0
    with torch.no_grad():
        for data in val_dataloader:
            # 只需要音频、图像和分类标签
            spec, image, _, _, _, cls, _ = (d.to(device) for d in data)

            # 模型只输出分类结果
            c = model(spec, image)
            loss_cls = cross_entropy_loss(c, cls)

            # 只使用分类损失
            loss = loss_cls
            total_loss += loss.item()

    return total_loss / len(val_dataloader)


def train_and_validate(model, train_dataloader, val_dataloader, optimizer, device, cross_entropy_loss, epochs, save_path):
    """
    Train and validate the model for classification task only.

    Args:
        model: The PyTorch model.
        train_dataloader: DataLoader for training data.
        val_dataloader: DataLoader for validation data.
        optimizer: The optimizer.
        device: The device (CPU or GPU).
        cross_entropy_loss: Loss function for classification.
        epochs: Number of epochs to train.
        save_path: Path to save the model weights.
    """
    val_min = float('inf')

    for epoch in range(epochs):
        print(f"Epoch {epoch + 1}/{epochs}")

        train_loss = train_one_epoch(model, train_dataloader, optimizer, device, cross_entropy_loss)
        print(f"Train Loss: {train_loss:.4f}")
        if epoch%10==0:
            val_loss = validate_one_epoch(model, val_dataloader, device, cross_entropy_loss)
            print(f"Validation Loss: {val_loss:.4f}")

            if val_loss < val_min:
                val_min = val_loss
                torch.save(model.state_dict(), f'{save_path}/best_epoch.pth')

            torch.save(model.state_dict(), f'{save_path}/last_epoch.pth')
