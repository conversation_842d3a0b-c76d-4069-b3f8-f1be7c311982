import os
import torch
import cv2
import numpy as np
from torch.utils.data.dataset import Dataset
from preprocess.audio_process import *
from preprocess.image_process import *
import torchvision.transforms as trans

np.random.seed(42)

class AntidroneLoader(Dataset):
    def __init__(self, annotation_lines,audio_path,image_path,gt_path,frequency=[1000,8000],dark_aug=0,audio_seq=0):
    #去掉detection path
    #def __init__(self, annotation_lines,audio_path,image_path,detect_path,gt_path,frequency=[1000,8000],dark_aug=0,audio_seq=0):
        super(AntidroneLoader, self).__init__()
        self.annotation_lines   = annotation_lines
        self.audio_path         = audio_path
        self.image_path         = image_path
        self.gt_path            = gt_path
        self.frequency          = frequency
        self.dark_aug           = dark_aug
        self.audio_seq          = audio_seq

    def __len__(self):
        return len(self.annotation_lines)
    
    def __getitem__(self, index):
        #get item是数据集的索引，返回的是一个字典，包含音频，图像，热图，差分，目标，检测，类别，轨迹
        name       = self.annotation_lines[index]
        #添加音频，图像，目标，检测文件的路径
        audio_name  = os.path.join(self.audio_path,name[:-4]+'npy')
        image_name  = os.path.join(self.image_path,name[:-4]+'png')
        gt_name     = os.path.join(self.gt_path,name[:-4]+'npy')

        if self.audio_seq:#如果音频序列为1，则将音频序列加载到内存中
            audio   = make_seq_audio(self.audio_path,name[:-4]+'npy')
        else:
            audio   = np.load(audio_name[:])#如果音频序列为0，则将音频加载到内存中

        audio   = np.transpose(audio,[1,0])#将音频序列转换为[1,6]
        spec       = Audio2Spectrogram(audio,sr=48000,min_frequency=self.frequency[0],max_frequency=self.frequency[1]) # in shape [64,64] (w*h)
        spec       = spec.float()

        image  = cv2.imread(image_name,cv2.IMREAD_COLOR)[:,:1280,:]#读取图像
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB) #to rgb
        #image,brightness  = image_darkaug(image,self.dark_aug)
        image         = np.transpose(image,[2,0,1])
        image         = torch.from_numpy(image).float()
        resize_transform = trans.Resize((256,256),antialias=True)
        image  = resize_transform(image)

        gt      = np.load(gt_name)
        heatmap,diff     = obtain_gaussian([512,512],gt.astype(np.float32))
        z = gt[-1:]
        heatmap = torch.from_numpy(heatmap).float()
        z       = torch.from_numpy(z).float()
        diff    = torch.from_numpy(diff).float()

        cls      = make_class(name)
        cls      = cls[0]
        cls = torch.tensor(cls)

        traj    = make_traj(self.gt_path,name[:-4]+'npy')
        traj    = torch.from_numpy(traj).float()

        #detect貌似是一个label,内容是0,1代表是否存在目标
        #(heatmap,diff)表示热力图用的，z是gt的名字,detect是0/1代表有无，cls是无人机类别，traj是轨迹（真正的gt）。
        return spec,image,heatmap,diff,z,cls,traj
