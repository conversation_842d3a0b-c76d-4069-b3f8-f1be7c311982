import torch.nn as nn
import torch.nn.functional as F

class DetectNet(nn.Module):
    def __init__(self, feature_dim=512, num_class=6):
        super(DetectNet, self).__init__()
        self.feature_dim = feature_dim
        self.num_class   = num_class

        #  classification heads only
        self.cls1 = nn.Linear(self.feature_dim, 256)
        self.cls2 = nn.Linear(256, 128)
        self.cls3 = nn.Linear(128, self.num_class)
        self.dropout = nn.Dropout(0.5)
    def forward(self, f_all):
        # Predict class only
        class_detection = F.relu(self.cls1(f_all))
        class_detection = self.dropout(class_detection)
        class_detection = F.relu(self.cls2(class_detection))
        class_detection = self.cls3(class_detection)

        return class_detection


